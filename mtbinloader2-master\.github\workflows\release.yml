name: Release

on:
  push:
    tags:
      - "*"

jobs:
  build:
    strategy:
      matrix:
        target: 
          - x86_64-linux-android
          - aarch64-linux-android          
          - armv7-linux-androideabi
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust
        run: rustup update stable
      - name: Install cross-compilation tools
        uses: taiki-e/setup-cross-toolchain-action@v1
        with:
          target: ${{ matrix.target }} 
      - name: build
        run: cargo build --profile githubci
        env:
          CARGO_PROFILE_RELEASE_LTO: "fat"
          CARGO_PROFILE_RELEASE_STRIP: "symbols"
          CARGO_PROFILE_RELEASE_CODEGEN_UNITS: 1
      - name: Build Changelog
        id: github_release
        uses: mikepenz/release-changelog-builder-action@v4
        with:
          commitMode: true
          configurationJson: |
            {
              "template": "#{{CHANGELOG}}",
              "categories": [
                {
                    "title": "## Feature",
                    "labels": ["feat", "feature"]
                },
                {
                    "title": "## Fix",
                    "labels": ["fix", "bug"]
                },

                {
                    "title": "## Chore",
                    "labels": ["chore"]
                }
                {
                    "title": "## Other",
                    "labels": []
                }
              ],
              "label_extractor": [
                {
                  "pattern": "^(build|chore|ci|docs|feat|fix|perf|refactor|revert|style|test){1}(\\([\\w\\-\\.]+\\))?(!)?: ([\\w ])+([\\s\\S]*)",
                  "target": "$1"
                }
              ],
            }
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Prepare lib for release
        run: mv target/${{ matrix.target }}/githubci/libmtbinloader2.so libmtbinloader2_${{ matrix.target }}.so
      - name: Create Release
        uses: mikepenz/action-gh-release@v0.2.0-a03 #softprops/action-gh-release
        with:
          body: ${{steps.github_release.outputs.changelog}}
          files: libmtbinloader2_${{ matrix.target }}.so

